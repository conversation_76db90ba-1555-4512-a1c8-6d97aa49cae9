using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NJS.Application.CQRS.WorkBreakdownStructures.Commands;
using NJS.Application.Dtos;
using NJS.Domain.Database;
using NJS.Domain.Entities;
using NJS.Domain.Enums;
using NJS.Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace NJS.Application.CQRS.WorkBreakdownStructures.Handlers
{
    public class CreateWBSVersionCommandHandler : IRequestHandler<CreateWBSVersionCommand, string>
    {
        private readonly ProjectManagementContext _context;
        private readonly IWBSVersionRepository _wbsVersionRepository;
        private readonly ILogger<CreateWBSVersionCommandHandler> _logger;

        public CreateWBSVersionCommandHandler(
            ProjectManagementContext context,
            IWBSVersionRepository wbsVersionRepository,
            ILogger<CreateWBSVersionCommandHandler> logger)
        {
            _context = context;
            _wbsVersionRepository = wbsVersionRepository;
            _logger = logger;
        }

        public async Task<string> Handle(CreateWBSVersionCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Get the current WBS for the project
                var wbs = await _context.WorkBreakdownStructures
                    .Include(w => w.Tasks)
                        .ThenInclude(t => t.PlannedHours)
                    .Include(w => w.Tasks)
                        .ThenInclude(t => t.UserWBSTasks)
                    .FirstOrDefaultAsync(w => w.ProjectId == request.ProjectId, cancellationToken);

                if (wbs == null)
                {
                    throw new InvalidOperationException($"WBS not found for project {request.ProjectId}");
                }

                // Get the next version number using the repository logic
                string nextVersion;

                if (request.IsApproved)
                {
                    // If this is an approval-based version creation, create the next major version
                    nextVersion = await _wbsVersionRepository.GetNextMajorVersionNumberAsync(request.ProjectId);
                }
                else
                {
                    // For regular updates, use the repository's next version logic
                    nextVersion = await _wbsVersionRepository.GetNextVersionNumberAsync(request.ProjectId);
                }

                // Check if this version already exists to prevent duplicates
                var versionExists = await _wbsVersionRepository.VersionExistsAsync(request.ProjectId, nextVersion);
                if (versionExists)
                {
                    _logger.LogInformation("WBS version {Version} already exists for project {ProjectId}, skipping creation", nextVersion, request.ProjectId);
                    return nextVersion; // Return the existing version number
                }

                // Create new WBS version
                var wbsVersion = new WBSVersionHistory
                {
                    WorkBreakdownStructureId = wbs.Id,
                    Version = nextVersion,
                    Comments = request.Comments,
                    CreatedBy = "system", // This should come from the current user context
                    StatusId = (int)PMWorkflowStatusEnum.Initial,
                    IsLatest = true,
                    IsActive = false // New versions are not active by default
                };

                // Deactivate all previous versions
                var existingVersions = await _wbsVersionRepository.GetByProjectIdAsync(request.ProjectId);
                foreach (var version in existingVersions.Where(v => v.IsLatest))
                {
                    version.IsLatest = false;
                    await _wbsVersionRepository.UpdateVersionAsync(version);
                }

                // Save the new version
                await _wbsVersionRepository.CreateVersionAsync(wbsVersion);

                // Copy tasks to version history
                if (request.IsApproved && request.Tasks != null && request.Tasks.Count > 0)
                {
                    // For approval-based versions, use the provided tasks (convert from DTO)
                    await CopyTasksFromDto(request.Tasks, wbsVersion.Id);
                }
                else
                {
                    // For regular updates or approval-based versions without provided tasks, copy current WBS tasks
                    await CopyTasksToVersion(wbs.Tasks, wbsVersion.Id);
                }

                // Update the WBS to point to the latest version
                wbs.LatestVersionHistoryId = wbsVersion.Id;
                wbs.CurrentVersion = nextVersion;
                _context.Entry(wbs).State = EntityState.Modified;
                await _context.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Created WBS version {NextVersion} for project {ProjectId}", nextVersion, request.ProjectId);

                return nextVersion;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating WBS version for project {ProjectId}", request.ProjectId);
                throw;
            }
        }

        private async Task CopyTasksFromDto(List<WBSTaskDto> taskDtos, int wbsVersionHistoryId)
        {
            var taskMap = new Dictionary<int, int>(); // Original task ID -> Version task ID

            // First pass: Create all tasks from DTOs
            foreach (var taskDto in taskDtos.OrderBy(t => t.DisplayOrder))
            {
                var taskVersion = new WBSTaskVersionHistory
                {
                    WBSVersionHistoryId = wbsVersionHistoryId,
                    OriginalTaskId = taskDto.Id,
                    ParentId = null, // Will be updated in second pass
                    Level = taskDto.Level,
                    Title = taskDto.Title,
                    Description = taskDto.Description,
                    DisplayOrder = taskDto.DisplayOrder,
                    EstimatedBudget = taskDto.EstimatedBudget,
                    StartDate = taskDto.StartDate,
                    EndDate = taskDto.EndDate,
                    TaskType = taskDto.TaskType
                };

                await _wbsVersionRepository.CreateTaskVersionAsync(taskVersion);
                taskMap[taskDto.Id] = taskVersion.Id;
            }

            // Second pass: Update parent relationships
            foreach (var taskDto in taskDtos)
            {
                if (taskDto.ParentId.HasValue && taskMap.TryGetValue(taskDto.ParentId.Value, out int parentVersionId))
                {
                    var taskVersion = await _wbsVersionRepository.GetTaskVersionByIdAsync(taskMap[taskDto.Id]);
                    taskVersion.ParentId = parentVersionId;
                    await _wbsVersionRepository.UpdateTaskVersionAsync(taskVersion);
                }
            }

            // Copy planned hours and user assignments from DTOs
            foreach (var taskDto in taskDtos)
            {
                var taskVersion = await _wbsVersionRepository.GetTaskVersionByIdAsync(taskMap[taskDto.Id]);

                // Copy planned hours if available
                if (taskDto.PlannedHours != null)
                {
                    foreach (var plannedHourDto in taskDto.PlannedHours)
                    {
                        var plannedHourVersion = new WBSTaskPlannedHourVersionHistory
                        {
                            WBSTaskVersionHistoryId = taskVersion.Id,
                            Year = plannedHourDto.Year.ToString(),
                            Month = plannedHourDto.Month,
                            PlannedHours = plannedHourDto.PlannedHours,
                            CreatedBy = "system"
                        };
                        await _wbsVersionRepository.CreatePlannedHourVersionAsync(plannedHourVersion);
                    }
                }

                // Copy user assignments if available (for manpower tasks)
                if (taskDto.TaskType == TaskType.Manpower && !string.IsNullOrEmpty(taskDto.AssignedUserId))
                {
                    var userAssignmentVersion = new UserWBSTaskVersionHistory
                    {
                        WBSTaskVersionHistoryId = taskVersion.Id,
                        UserId = taskDto.AssignedUserId,
                        ResourceRoleId = taskDto.ResourceRoleId
                    };
                    await _wbsVersionRepository.CreateUserAssignmentVersionAsync(userAssignmentVersion);
                }
                else if (taskDto.TaskType == TaskType.ODC && !string.IsNullOrEmpty(taskDto.ResourceName))
                {
                    // For ODC tasks, create assignment with resource name
                    var userAssignmentVersion = new UserWBSTaskVersionHistory
                    {
                        WBSTaskVersionHistoryId = taskVersion.Id,
                        UserId = null, // ODC tasks don't have UserId
                        ResourceRoleId = taskDto.ResourceRoleId
                    };
                    await _wbsVersionRepository.CreateUserAssignmentVersionAsync(userAssignmentVersion);
                }
            }
        }

        private async Task CopyTasksToVersion(ICollection<WBSTask> tasks, int wbsVersionHistoryId)
        {
            var taskMap = new Dictionary<int, int>(); // Original task ID -> Version task ID

            // First pass: Create all tasks
            foreach (var task in tasks.OrderBy(t => t.DisplayOrder))
            {
                var taskVersion = new WBSTaskVersionHistory
                {
                    WBSVersionHistoryId = wbsVersionHistoryId,
                    OriginalTaskId = task.Id,
                    ParentId = null, // Will be updated in second pass
                    Level = task.Level,
                    Title = task.Title,
                    Description = task.Description,
                    DisplayOrder = task.DisplayOrder,
                    EstimatedBudget = task.EstimatedBudget,
                    StartDate = task.StartDate,
                    EndDate = task.EndDate,
                    TaskType = task.TaskType
                };

                await _wbsVersionRepository.CreateTaskVersionAsync(taskVersion);
                taskMap[task.Id] = taskVersion.Id;
            }

            // Second pass: Update parent relationships
            foreach (var task in tasks)
            {
                if (task.ParentId.HasValue && taskMap.TryGetValue(task.ParentId.Value, out int parentVersionId))
                {
                    var taskVersion = await _wbsVersionRepository.GetTaskVersionByIdAsync(taskMap[task.Id]);
                    taskVersion.ParentId = parentVersionId;
                    await _wbsVersionRepository.UpdateTaskVersionAsync(taskVersion);
                }
            }

            // Copy planned hours and user assignments
            foreach (var task in tasks)
            {
                var taskVersion = await _wbsVersionRepository.GetTaskVersionByIdAsync(taskMap[task.Id]);
                
                // Copy planned hours
                foreach (var plannedHour in task.PlannedHours)
                {
                    var plannedHourVersion = new WBSTaskPlannedHourVersionHistory
                    {
                        WBSTaskVersionHistoryId = taskVersion.Id,
                        Year = plannedHour.Year,
                        Month = plannedHour.Month,
                        PlannedHours = plannedHour.PlannedHours,
                        CreatedBy = "system"
                    };
                    await _wbsVersionRepository.CreatePlannedHourVersionAsync(plannedHourVersion);
                }

                // Copy user assignments
                foreach (var userAssignment in task.UserWBSTasks)
                {
                    var userAssignmentVersion = new UserWBSTaskVersionHistory
                    {
                        WBSTaskVersionHistoryId = taskVersion.Id,
                        UserId = userAssignment.UserId,
                        ResourceRoleId = userAssignment.ResourceRoleId
                    };
                    await _wbsVersionRepository.CreateUserAssignmentVersionAsync(userAssignmentVersion);
                }
            }
        }
    }
}
